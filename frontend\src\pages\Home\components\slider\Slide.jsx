import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';

const HeroSlider = ({ slides }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [progress, setProgress] = useState(0);
  const intervalRef = useRef(null);
  const progressIntervalRef = useRef(null);

  const autoplaySpeed = 6000; // 6 seconds per slide
  const progressUpdateInterval = 50; // Update progress every 50ms

  // Sample slides data if none provided
  const defaultSlides = [
    {
      _id: '1',
      imageData: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80',
      title: 'Transform Your Business',
      subtitle: 'Innovative solutions that drive growth and success in the digital age.',
      buttonText: 'Get Started',
      buttonLink: '/get-started'
    },
    {
      _id: '2',
      imageData: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
      title: 'Unleash Innovation',
      subtitle: 'Cutting-edge technology and creative strategies for modern challenges.',
      buttonText: 'Learn More',
      buttonLink: '/services'
    },
    {
      _id: '3',
      imageData: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2071&q=80',
      title: 'Build the Future',
      subtitle: 'Join thousands of companies already transforming their operations.',
      buttonText: 'Join Now',
      buttonLink: '/contact'
    }
  ];

  const slidesData = slides || defaultSlides;

  // Auto-advance slides
  useEffect(() => {
    if (isAutoPlaying) {
      intervalRef.current = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % slidesData.length);
      }, autoplaySpeed);

      progressIntervalRef.current = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            return 0;
          }
          return prev + (100 / (autoplaySpeed / progressUpdateInterval));
        });
      }, progressUpdateInterval);
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (progressIntervalRef.current) clearInterval(progressIntervalRef.current);
    };
  }, [isAutoPlaying, slidesData.length]);

  // Reset progress when slide changes
  useEffect(() => {
    setProgress(0);
  }, [currentSlide]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
    setProgress(0);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slidesData.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slidesData.length) % slidesData.length);
  };

  const toggleAutoplay = () => {
    setIsAutoPlaying(!isAutoPlaying);
  };

  return (
    <div className="relative h-screen overflow-hidden group bg-black">
      {/* Background Images */}
      <div className="absolute inset-0">
        {slidesData.map((slide, index) => (
          <div
            key={slide._id}
            className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
              index === currentSlide 
                ? 'opacity-100 scale-100' 
                : 'opacity-0 scale-110'
            }`}
          >
            <img 
              src={slide.imageData} 
              alt={slide.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-transparent" />
          </div>
        ))}
      </div>

      {/* Content */}
      <div className="relative z-20 h-full flex items-center">
        <div className="container mx-auto px-6 lg:px-12">
          <div className="max-w-4xl">
            {slidesData.map((slide, index) => (
              <div
                key={slide._id}
                className={`transition-all duration-700 ease-out ${
                  index === currentSlide
                    ? 'opacity-100 transform translate-y-0'
                    : 'opacity-0 transform -translate-y-8'
                }`}
                style={{ display: index === currentSlide ? 'block' : 'none' }}
              >
                <div className="space-y-6">
                  <h1 className="text-5xl lg:text-7xl xl:text-8xl font-black text-white leading-tight">
                    <span className="block overflow-hidden">
                      <span className="block animate-slide-up">{slide.title}</span>
                    </span>
                  </h1>
                  
                  <p className="text-xl lg:text-2xl text-white/90 max-w-2xl leading-relaxed animate-slide-up-delay">
                    {slide.subtitle}
                  </p>
                  
                  <div className="flex flex-wrap gap-4 pt-4 animate-slide-up-delay-2">
                    {/* === IMPROVED BUTTON START === */}
                    <a
                      href={slide.buttonLink}
                      className="group inline-block rounded-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-1 text-lg font-bold text-white transition-transform duration-300 ease-in-out hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-300/50 active:scale-95"
                    >
                      <span className="block rounded-full bg-gray-900/70 px-8 py-3 backdrop-blur-sm transition-colors duration-300 group-hover:bg-transparent">
                        {slide.buttonText}
                      </span>
                    </a>
                    {/* === IMPROVED BUTTON END === */}
                    
                    <button className="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold text-lg rounded-full backdrop-blur-sm hover:bg-white/10 hover:border-white/60 transition-all duration-300">
                      Watch Demo
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-6 top-1/2 -translate-y-1/2 z-30 p-3 bg-white/10 backdrop-blur-sm rounded-full text-white hover:bg-white/20 hover:scale-110 transition-all duration-300 opacity-0 group-hover:opacity-100"
        aria-label="Previous Slide"
      >
        <ChevronLeft size={24} />
      </button>
      
      <button
        onClick={nextSlide}
        className="absolute right-6 top-1/2 -translate-y-1/2 z-30 p-3 bg-white/10 backdrop-blur-sm rounded-full text-white hover:bg-white/20 hover:scale-110 transition-all duration-300 opacity-0 group-hover:opacity-100"
        aria-label="Next Slide"
      >
        <ChevronRight size={24} />
      </button>

      {/* Bottom Controls */}
      <div className="absolute bottom-0 left-0 right-0 z-30 p-6">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            {/* Slide Indicators with Progress */}
            <div className="flex items-center space-x-4">
              {slidesData.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className="group relative"
                  aria-label={`Go to slide ${index + 1}`}
                >
                  <div className={`w-12 h-1 rounded-full transition-all duration-300 ${
                    index === currentSlide ? 'bg-white' : 'bg-white/30 hover:bg-white/60'
                  }`}>
                    {index === currentSlide && (
                      <div 
                        className="h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full"
                        style={{ width: `${progress}%`, transition: 'width 0.1s linear' }}
                      />
                    )}
                  </div>
                  <span className="absolute -top-8 left-1/2 -translate-x-1/2 text-xs text-white opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap px-2 py-1 bg-black/50 rounded-md">
                    Slide {index + 1}
                  </span>
                </button>
              ))}
            </div>

            {/* Autoplay Toggle */}
            
          </div>
        </div>
      </div>

      {/* Slide Counter */}
      <div className="absolute top-6 right-6 z-30">
        <div className="px-4 py-2 bg-black/30 backdrop-blur-sm rounded-full text-white font-mono text-sm">
          {String(currentSlide + 1).padStart(2, '0')} / {String(slidesData.length).padStart(2, '0')}
        </div>
      </div>

      <style jsx>{`
        @keyframes slide-up {
          from {
            opacity: 0;
            transform: translateY(60px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-slide-up {
          animation: slide-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .animate-slide-up-delay {
          animation: slide-up 0.8s 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
          opacity: 0;
        }

        .animate-slide-up-delay-2 {
          animation: slide-up 0.8s 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
          opacity: 0;
        }
      `}</style>
    </div>
  );
};

export default HeroSlider;